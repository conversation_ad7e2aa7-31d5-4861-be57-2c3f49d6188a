# DODO was here
"""add on_repair and deprecated columns to dashboard

Revision ID: b25a63d72f8c
Revises: 48cbb571fa3a
Create Date: 2025-06-20 16:42:01.322420

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b25a63d72f8c"
down_revision = "48cbb571fa3a"


def upgrade():
    op.add_column(
        "dashboards", sa.Column("on_repair", sa.<PERSON>(), nullable=False, server_default=sa.false())
    )
    op.add_column(
        "dashboards", sa.Column("deprecated", sa.<PERSON>(), nullable=False, server_default=sa.false())
    )

def downgrade():
    op.drop_column("dashboards", "on_repair")
    op.drop_column("dashboards", "deprecated")
